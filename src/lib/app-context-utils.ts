import { DirectMessage, Message, Section, Channel, Thread, Workspace, UserSettings, WorkspaceSettings, User } from './types';

/**
 * Removes duplicate DM conversations.
 * Keeps the first DM for each unique participant pairing,
 * preferring DMs with messages if multiple exist for the same pair.
 */
export const deduplicateDirectMessages = (dms: DirectMessage[], currentUserId: string): DirectMessage[] => {
  const userDmMap = new Map<string, DirectMessage>();

  for (const dm of dms) {
    if (dm.participants.length === 2) {
      const otherUserId = dm.participants.find(id => id !== currentUserId);
      if (otherUserId) {
        const existingDm = userDmMap.get(otherUserId);
        if (!existingDm || (dm.messages && dm.messages.length > 0 && (!existingDm.messages || existingDm.messages.length === 0))) {
          userDmMap.set(otherUserId, dm);
        }
      }
    } else {
      // For group DMs or DMs with self (if allowed), use a unique key
      // This simple keying might need refinement for complex group DM scenarios
      const key = dm.participants.sort().join('-');
      if (!userDmMap.has(key) || (dm.messages && dm.messages.length > 0 && (!userDmMap.get(key)?.messages?.length))) {
        userDmMap.set(key, dm);
      }
    }
  }
  return Array.from(userDmMap.values());
};

/**
 * Finds a section by its ID from a list of sections.
 */
export const findSectionById = (sections: Section[], sectionId: string | null): Section | undefined => {
  if (!sectionId) return undefined;
  return sections.find(section => section.id === sectionId);
};

/**
 * Finds a channel by its ID from a list of sections.
 */
export const findChannelById = (sections: Section[], channelId: string | null): Channel | undefined => {
  if (!channelId) return undefined;
  for (const section of sections) {
    const channel = section.channels.find(ch => ch.id === channelId);
    if (channel) return channel;
  }
  return undefined;
};

/**
 * Transforms a raw message object from Supabase (e.g., from a Realtime payload or RPC response)
 * into the client-side Message type.
 */
export const transformSupabaseMessage = (dbMessage: any): Message => {
  // Basic transformation
  const message: Message = {
    id: dbMessage.id,
    content: dbMessage.content,
    timestamp: dbMessage.timestamp || dbMessage.created_at, // Prefer 'timestamp' if already transformed, else 'created_at'
    userId: dbMessage.user_id,
    channelId: dbMessage.dm_id || dbMessage.channel_id, // dm_id takes precedence for channelId field
    threadId: dbMessage.parent_message_id,
    topicId: dbMessage.topic_id,
    // Initialize optional fields that might not be in every dbMessage
    reactions: dbMessage.reactions || [],
    editedTimestamp: dbMessage.edited_at,
    files: dbMessage.files?.map((f: any) => ({
      id: f.id,
      name: f.name,
      type: f.type,
      url: f.url,
      size_bytes: f.size_bytes,
      uploaded_by_user_id: f.uploaded_by_user_id,
      created_at: f.created_at,
      message_id: f.message_id,
      channel_id: f.channel_id,
      is_pinned_in_channel_id: f.is_pinned_in_channel_id,
      // displayId: generateDisplayId(f.id, 'file-'), // generateDisplayId is not available here, can be added if needed or handled at display time
    })) || [],
    alsoSendToChannel: dbMessage.also_send_to_channel,
    // Add other fields from Message type if they can come from dbMessage and need default values
  };

  // If the dbMessage itself has a 'user' object (e.g. from a join in an RPC)
  // and our Message type expects a simple userId, we ensure only userId is set.
  // If Message type expects a nested User object, this would be different.
  // Current Message type seems to expect userId (string).

  return message;
};

/**
 * Organizes messages into threads and regular messages.
 * Thread replies (messages with parentMessageId) are moved to the threads structure.
 * @param messages Array of messages to organize
 * @returns Object with organized messages and threads
 */
export const organizeMessagesIntoThreads = (messages: Message[]): {
  regularMessages: Message[],
  threads: Record<string, Thread>
} => {
  const regularMessages: Message[] = [];
  const threads: Record<string, Thread> = {};

  messages.forEach(message => {
    if (message.threadId) {
      // This is a thread reply
      const threadId = message.threadId;

      if (!threads[threadId]) {
        // Find the parent message to create the thread
        const parentMessage = messages.find(m => m.id === threadId);
        threads[threadId] = {
          id: threadId,
          parentMessageId: threadId,
          messages: parentMessage ? [parentMessage] : []
        };
      }

      // Add this reply to the thread if it's not already there
      if (!threads[threadId].messages.some(m => m.id === message.id)) {
        threads[threadId].messages.push(message);
        // Sort thread messages oldest-first
        threads[threadId].messages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      }
    } else {
      // This is a regular message (not a thread reply)
      regularMessages.push(message);
    }
  });

  return { regularMessages, threads };
};

/**
 * Finds an existing thread or creates a new one from a parent message within a given context (channel or DM).
 * @param messageId The ID of the parent message for the thread.
 * @param channelOrDmContext The channel or DM object that might contain the message or thread.
 * @returns The found or newly created Thread object, or null if the parent message isn't found.
 */
export const findOrCreateThreadFromContext = (
  messageId: string,
  channelOrDmContext: Channel | DirectMessage | null
): Thread | null => {
  if (!channelOrDmContext) return null;

  // Check if thread already exists in the context's threads record
  if (channelOrDmContext.threads && channelOrDmContext.threads[messageId]) {
    return channelOrDmContext.threads[messageId];
  }

  // If not, try to find the parent message in the context's messages list
  const parentMessage = channelOrDmContext.messages.find(m => m.id === messageId);
  if (parentMessage) {
    // Create a new thread object with the parent message as its first message
    return {
      id: messageId, // Thread ID is the same as the parent message ID
      parentMessageId: messageId,
      messages: [parentMessage] // Start with the parent message
    };
  }
  return null; // Message not found, cannot create thread
};

/**
 * Updates an optimistically added message in an array with its final version from the database.
 * This function mutates the provided array if the optimistic message is found.
 * @param messagesArray The array of messages to update.
 * @param clientTempId The temporary ID of the optimistic message.
 * @param finalMessage The final message object (transformed from DB response).
 * @returns The (potentially mutated) messages array.
 */
export const updateOptimisticMessageInArray = (
  messagesArray: Message[],
  clientTempId: string,
  finalMessage: Message
): Message[] => {
  const msgIndex = messagesArray.findIndex(m => m.id === clientTempId);
  let updatedMessages = [...messagesArray]; // Work on a copy

  if (msgIndex !== -1) {
    const optimisticEntry = updatedMessages[msgIndex];
    updatedMessages[msgIndex] = {
      ...optimisticEntry, // Start with optimistic data
      ...finalMessage,    // Overlay with final data from DB
      // Explicitly preserve optimistic files if finalMessage doesn't have them (which it won't in Phase 1/2 from send_message RPC)
      files: (finalMessage.files && finalMessage.files.length > 0) ? finalMessage.files : optimisticEntry.files
    };
    // console.log('[Optimistic Success Utility] Updated message', clientTempId, 'to', finalMessage.id, 'in state. Preserved optimistic files if necessary.');
  }
  // Ensure the array is sorted oldest-first after update
  return updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
};

/**
 * Merges newly loaded workspace data with a previous workspace state.
 * It prioritizes the structure and top-level properties from `loadedWorkspace`
 * but attempts to preserve dynamically loaded content (like messages, threads)
 * from `prevWorkspace` for existing channels and DMs.
 * @param prevWorkspace The previous workspace state, can be null.
 * @param loadedWorkspace The newly fetched workspace data.
 * @returns The merged workspace state.
 */
export const mergeWorkspaceData = (
  prevWorkspace: Workspace | null,
  loadedWorkspace: Workspace
): Workspace => {
  if (!prevWorkspace) return loadedWorkspace; // No previous state, use new one directly

  // Create a new workspace object based on loadedWorkspace
  const newMergedWorkspace: Workspace = {
    ...prevWorkspace, // Start with previous to keep things not in loadedWorkspace (e.g. if loadedWorkspace is partial)
    ...loadedWorkspace, // Overwrite with new top-level data (id, name, current selections, users, settings)
  };

  // Iterate over sections and channels of the new workspace data to merge
  newMergedWorkspace.sections = loadedWorkspace.sections.map(newSection => {
    const prevSection = prevWorkspace.sections.find(ps => ps.id === newSection.id);
    return {
      ...newSection, // Structure from new fetch (e.g. name, order)
      channels: newSection.channels.map(newChannel => {
        const prevChannel = prevSection?.channels.find(pc => pc.id === newChannel.id);
        if (prevChannel) {
          // If channel existed, preserve its dynamically loaded content
          return {
            ...newChannel, // Basic structure from new fetch (name, description, etc.)
            messages: prevChannel.messages || [],
            threads: prevChannel.threads || {},
            channelTopics: prevChannel.channelTopics || [],
            files: prevChannel.files || [],
            unreadCount: prevChannel.unreadCount !== undefined ? prevChannel.unreadCount : newChannel.unreadCount, // Preserve existing unread, else take new
            // Ensure other properties from newChannel are taken if they are meant to be updated
          };
        }
        // It's a new channel not seen before, or prevSection didn't exist. Use as is from loadedWorkspace.
        return newChannel; // newChannel already has messages: [], threads: {} etc. as per getInitialWorkspaceDataForUser
      }),
    };
  });

  // Merge direct messages similarly
  if (loadedWorkspace.directMessages && loadedWorkspace.directMessages.length > 0) {
    newMergedWorkspace.directMessages = loadedWorkspace.directMessages.map(newDM => {
      const prevDM = prevWorkspace.directMessages.find(pdm => pdm.id === newDM.id);
      if (prevDM) {
        return {
          ...newDM,
          messages: prevDM.messages || [],
          threads: prevDM.threads || {},
          topics: prevDM.topics || [], // Assuming topics for DMs
          unreadCount: prevDM.unreadCount !== undefined ? prevDM.unreadCount : newDM.unreadCount, // Preserve existing unread, else take new
        };
      }
      return newDM;
    });
  } else if (prevWorkspace.directMessages && prevWorkspace.directMessages.length > 0 && (!loadedWorkspace.directMessages || loadedWorkspace.directMessages.length === 0)) {
    // If new data has no DMs but old one did, keep old DMs (unless explicitly cleared by backend logic)
    newMergedWorkspace.directMessages = prevWorkspace.directMessages;
  } else {
    // If loadedWorkspace.directMessages is explicitly empty or undefined, ensure it's set
    newMergedWorkspace.directMessages = loadedWorkspace.directMessages || [];
  }

  // Ensure current selections, users, and settings are taken from loadedWorkspace as it's the source of truth for these.
  // The spread operator for loadedWorkspace at the beginning of newMergedWorkspace definition handles most of this.
  // Explicitly re-assigning just to be clear on intent for these critical top-level props.
  newMergedWorkspace.currentChannelId = loadedWorkspace.currentChannelId;
  newMergedWorkspace.currentSectionId = loadedWorkspace.currentSectionId;
  newMergedWorkspace.currentDirectMessageId = loadedWorkspace.currentDirectMessageId;
  newMergedWorkspace.activeThreadId = loadedWorkspace.activeThreadId;
  newMergedWorkspace.users = loadedWorkspace.users;
  newMergedWorkspace.settings = loadedWorkspace.settings;

  return newMergedWorkspace;
};

/**
 * Applies a new realtime message to the workspace state.
 * Handles adding the message to the correct channel/DM/thread,
 * avoids duplicates, and updates unread counts for non-active conversations.
 * @param prevWs The previous workspace state.
 * @param newMessage The new message (already transformed to client-side Message type).
 * @param currentAuthUserId The ID of the currently authenticated user.
 * @returns The new workspace state with the message applied.
 */
export const applyRealtimeMessageToWorkspace = (
  prevWs: Workspace, // Assuming prevWs is never null when this is called
  newMessage: Message,
  currentAuthUserId: string
): Workspace => {
  // Check if the message belongs to any channel or DM in the current workspace
  let targetConversationExists = false;
  if (newMessage.channelId) {
    if (prevWs.directMessages.some(dm => dm.id === newMessage.channelId)) {
      targetConversationExists = true;
    } else {
      for (const section of prevWs.sections) {
        if (section.channels.some(ch => ch.id === newMessage.channelId)) {
          targetConversationExists = true;
          break;
        }
      }
    }
  }

  if (!targetConversationExists) {
    // console.log('Realtime Util: Message does not belong to a known channel/DM in this workspace. Ignoring.', newMessage);
    return prevWs;
  }

  let messageAlreadyExists = false;
  const newWs = JSON.parse(JSON.stringify(prevWs)); // Deep clone

  if (newMessage.threadId) { // It's a thread reply
    const threadParentChannelId = newMessage.channelId;
    if (threadParentChannelId) {
      const ch = newWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === threadParentChannelId);
      const dm = newWs.directMessages.find((d: DirectMessage) => d.id === threadParentChannelId);
      const parentContext = ch || dm;
      if (parentContext && parentContext.threads[newMessage.threadId]) {
        if (parentContext.threads[newMessage.threadId].messages.some((m: Message) => m.id === newMessage.id)) {
          messageAlreadyExists = true;
        } else {
          parentContext.threads[newMessage.threadId].messages.push(newMessage);
          // Sort thread messages oldest-first
          parentContext.threads[newMessage.threadId].messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        }
      } else if (parentContext) { // Thread doesn't exist locally yet, create it
        parentContext.threads[newMessage.threadId] = { id: newMessage.threadId, parentMessageId: newMessage.threadId, messages: [newMessage] };
        // Messages array already contains just one message, so it's sorted.
      }
    }
  } else if (newMessage.channelId) { // It's a top-level message
    const ch = newWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === newMessage.channelId);
    const dm = newWs.directMessages.find((d: DirectMessage) => d.id === newMessage.channelId);
    const targetConversation = ch || dm;
    if (targetConversation) {
      if (targetConversation.messages.some((m: Message) => m.id === newMessage.id)) {
        messageAlreadyExists = true;
      } else {
        targetConversation.messages.push(newMessage);
        // Sort conversation messages oldest-first
        targetConversation.messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      }
    }
  }

  if (messageAlreadyExists) {
    // console.log('Realtime Util: Message already exists in state. Ignoring.', newMessage.id);
    return prevWs;
  }

  // Update unread count if the message is for a non-active conversation and not from the current user
  if (newMessage.userId !== currentAuthUserId) {
    const isChannelMessage = newWs.sections.some((s: Section) => s.channels.some((c: Channel) => c.id === newMessage.channelId));
    const isDmMessage = newWs.directMessages.some((dm: DirectMessage) => dm.id === newMessage.channelId);

    if (isChannelMessage && newMessage.channelId !== newWs.currentChannelId) {
      newWs.sections.forEach((s: Section) => {
        s.channels.forEach((c: Channel) => {
          if (c.id === newMessage.channelId) {
            c.unreadCount = (c.unreadCount || 0) + 1;
            c.isUnreadCountPlaceholder = false; // New message means it's not a placeholder
            // console.log(`Realtime Util: Incremented unread for channel ${c.name} to ${c.unreadCount}`);
          }
        });
      });
    } else if (isDmMessage && newMessage.channelId !== newWs.currentDirectMessageId) {
      let dmFoundAndUpdated = false;
      newWs.directMessages.forEach((dm: DirectMessage) => {
        if (dm.id === newMessage.channelId) {
          dmFoundAndUpdated = true;
          dm.unreadCount = (dm.unreadCount || 0) + 1;
          dm.isUnreadCountPlaceholder = false; // New message means it's not a placeholder
          // const participantNames = dm.participants.map(pId => newWs.users.find((u:User) => u.id === pId)?.name || 'Unknown').join(', ');
          // const dmName = dm.name || participantNames;
          // console.log(`Realtime Util: Incremented unread for DM "${dmName}" to ${dm.unreadCount}`);
        }
      });
      // if (!dmFoundAndUpdated) {
      //   console.log(`Realtime Util: DM with ID ${newMessage.channelId} not found for unread increment.`);
      // }
    }
  }
  // console.log('Realtime Util: Applied new message to state.', newMessage);
  return newWs;
};

/**
 * Applies the state update after successfully adding a new channel.
 * Adds the new channel to the specified section, sorts channels, and sets the new channel as current.
 * @param prevWs The previous workspace state.
 * @param newChannel The newly created Channel object.
 * @param targetSectionId The ID of the section to add the channel to.
 * @returns The new workspace state.
 */
export const applyAddChannelUpdateToWorkspace = (
  prevWs: Workspace,
  newChannel: Channel,
  targetSectionId: string
): Workspace => {
  const newSections = prevWs.sections.map(s =>
    s.id === targetSectionId
      ? { ...s, channels: [...s.channels, newChannel].sort((a, b) => a.name.localeCompare(b.name)) }
      : s
  );
  return {
    ...prevWs,
    sections: newSections,
    currentSectionId: targetSectionId,
    currentChannelId: newChannel.id,
    currentDirectMessageId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update after adding a new section.
 * @param prevWs The previous workspace state.
 * @param newSection The newly created Section object.
 * @returns The new workspace state.
 */
export const applyAddSectionUpdateToWorkspace = (
  prevWs: Workspace,
  newSection: Section
): Workspace => {
  return { ...prevWs, sections: [...prevWs.sections, newSection] };
};

/**
 * Applies the state update after adding a new direct message.
 * Adds the new DM and sets it as the current conversation.
 * @param prevWs The previous workspace state.
 * @param newDm The newly created DirectMessage object.
 * @returns The new workspace state.
 */
export const applyAddDirectMessageUpdateToWorkspace = (
  prevWs: Workspace,
  newDm: DirectMessage
): Workspace => {
  return {
    ...prevWs,
    directMessages: [...prevWs.directMessages, newDm],
    currentDirectMessageId: newDm.id,
    currentChannelId: null,
    currentSectionId: null,
    activeThreadId: null,
  };
};

/**
 * Applies an optimistic message update to the workspace state.
 * Adds a temporary message to the appropriate channel/DM/thread.
 * @param prevWs The previous workspace state.
 * @param optimisticMessage The temporary message to add.
 * @param targetChannelId The ID of the target channel (if any).
 * @param targetDMId The ID of the target DM (if any).
 * @param threadId The ID of the target thread (if any).
 * @returns The new workspace state with the optimistic message added.
 */
export const applyOptimisticMessageUpdate = (
  prevWs: Workspace,
  optimisticMessage: Message,
  targetChannelId: string | null | undefined,
  targetDMId: string | null | undefined,
  threadId: string | null | undefined
): Workspace => {
  const newWs = JSON.parse(JSON.stringify(prevWs)); // Deep clone

  if (threadId) {
    if (targetChannelId) {
      const ch = newWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
      if (ch) {
        ch.threads[threadId] = ch.threads[threadId] || { id: threadId, parentMessageId: threadId, messages: [] };
        ch.threads[threadId].messages.push(optimisticMessage);
        ch.threads[threadId].messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      }
    } else if (targetDMId) {
      const dm = newWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
      if (dm) {
        dm.threads[threadId] = dm.threads[threadId] || { id: threadId, parentMessageId: threadId, messages: [] };
        dm.threads[threadId].messages.push(optimisticMessage);
        dm.threads[threadId].messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      }
    }
  } else if (targetChannelId) {
    const ch = newWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
    if (ch) {
      ch.messages.push(optimisticMessage);
      ch.messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      // console.log('[Optimistic Update Util] Messages for channel', targetChannelId, 'after push:', JSON.parse(JSON.stringify(ch.messages)));
    }
  } else if (targetDMId) {
    const dm = newWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
    if (dm) {
      dm.messages.push(optimisticMessage);
      dm.messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      // console.log('[Optimistic Update Util] Messages for DM', targetDMId, 'after push:', JSON.parse(JSON.stringify(dm.messages)));
    }
  }
  return newWs;
};

/**
 * Reverts an optimistic message update from the workspace state.
 * Removes a temporary message from the appropriate channel/DM/thread.
 * @param prevWs The previous workspace state.
 * @param clientTempId The temporary ID of the message to remove.
 * @param targetChannelId The ID of the target channel (if any).
 * @param targetDMId The ID of the target DM (if any).
 * @param threadId The ID of the target thread (if any).
 * @returns The new workspace state with the optimistic message reverted.
 */
export const revertOptimisticMessageUpdate = (
  prevWs: Workspace,
  clientTempId: string,
  targetChannelId: string | null | undefined,
  targetDMId: string | null | undefined,
  threadId: string | null | undefined
): Workspace => {
  const revertedWs = JSON.parse(JSON.stringify(prevWs));

  if (threadId) {
    if (targetChannelId) {
      const ch = revertedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
      if (ch && ch.threads[threadId]) {
        ch.threads[threadId].messages = ch.threads[threadId].messages.filter((m: Message) => m.id !== clientTempId);
      }
    } else if (targetDMId) {
      const dm = revertedWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
      if (dm && dm.threads[threadId]) {
        dm.threads[threadId].messages = dm.threads[threadId].messages.filter((m: Message) => m.id !== clientTempId);
      }
    }
  } else if (targetChannelId) {
    const ch = revertedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
    if (ch) {
      ch.messages = ch.messages.filter((m: Message) => m.id !== clientTempId);
    }
  } else if (targetDMId) {
    const dm = revertedWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
    if (dm) {
      dm.messages = dm.messages.filter((m: Message) => m.id !== clientTempId);
    }
  }
  // console.log('[Optimistic Revert Util] Workspace state after reverting optimistic message', clientTempId);
  return revertedWs;
};

/**
 * Applies an update to a specific channel within the workspace state.
 * @param prevWs The previous workspace state.
 * @param updatedChannel The channel object with updated properties.
 * @returns The new workspace state.
 */
export const applyUpdateChannelToWorkspace = (
  prevWs: Workspace,
  updatedChannel: Channel
): Workspace => {
  const newSections = prevWs.sections.map(s => ({
    ...s,
    channels: s.channels.map(ch =>
      ch.id === updatedChannel.id ? updatedChannel : ch
    ),
  }));
  return { ...prevWs, sections: newSections };
};

/**
 * Applies the state update for setting the current section.
 * Clears current channel, DM, and active thread.
 * @param prevWs The previous workspace state.
 * @param sectionId The ID of the section to set as current, or null.
 * @returns The new workspace state.
 */
export const applySetCurrentSectionToWorkspace = (
  prevWs: Workspace,
  sectionId: string | null
): Workspace => {
  return {
    ...prevWs,
    currentSectionId: sectionId,
    currentChannelId: null,
    currentDirectMessageId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update for setting the current channel.
 * Sets the corresponding section, and clears current DM and active thread.
 * @param prevWs The previous workspace state.
 * @param channelId The ID of the channel to set as current, or null.
 * @param sectionIdForChannel The ID of the section containing the channel.
 * @returns The new workspace state.
 */
export const applySetCurrentChannelToWorkspace = (
  prevWs: Workspace,
  channelId: string | null,
  sectionIdForChannel: string | null
): Workspace => {
  return {
    ...prevWs,
    currentSectionId: sectionIdForChannel,
    currentChannelId: channelId,
    currentDirectMessageId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update for setting the current direct message.
 * Clears current channel, section, and active thread.
 * @param prevWs The previous workspace state.
 * @param directMessageId The ID of the DM to set as current, or null.
 * @returns The new workspace state.
 */
export const applySetCurrentDirectMessageToWorkspace = (
  prevWs: Workspace,
  directMessageId: string | null
): Workspace => {
  return {
    ...prevWs,
    currentDirectMessageId: directMessageId,
    currentChannelId: null,
    currentSectionId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update for setting the active thread.
 * Ensures the thread exists in the context (channel or DM).
 * @param prevWs The previous workspace state.
 * @param messageId The ID of the parent message of the thread, or null to clear.
 * @param activeContext The current channel or DM context.
 * @returns The new workspace state.
 */
export const applySetActiveThreadToWorkspace = (
  prevWs: Workspace,
  messageId: string | null,
  activeContext: Channel | DirectMessage | null
): Workspace => {
  if (!messageId) {
    return { ...prevWs, activeThreadId: null };
  }

  const thread = findOrCreateThreadFromContext(messageId, activeContext);
  if (!thread) {
    // console.error("[Util] Could not find or create thread for message:", messageId);
    return prevWs; // Return previous state if thread can't be established
  }

  let newWs = { ...prevWs };
  if (activeContext) {
    if ('channels' in activeContext) { // It's a Channel (hacky check, better to pass type or use type guard)
      const targetChannel = activeContext as Channel;
      const newSections = prevWs.sections.map(s => ({
        ...s,
        channels: s.channels.map(c =>
          c.id === targetChannel.id ? { ...c, threads: { ...c.threads, [messageId]: thread } } : c
        ),
      }));
      newWs = { ...prevWs, sections: newSections, activeThreadId: messageId };
    } else { // It's a DirectMessage
      const targetDM = activeContext as DirectMessage;
      const newDMs = prevWs.directMessages.map(dm =>
        dm.id === targetDM.id ? { ...dm, threads: { ...dm.threads, [messageId]: thread } } : dm
      );
      newWs = { ...prevWs, directMessages: newDMs, activeThreadId: messageId };
    }
  } else {
    // Fallback if activeContext is somehow null but messageId is provided
    // This case should ideally not be hit if logic in AppProvider is correct
    newWs = { ...prevWs, activeThreadId: messageId };
  }
  return newWs;
};

/**
 * Marks a conversation (channel or DM) as read by setting its unreadCount to 0.
 * @param prevWs The previous workspace state.
 * @param conversationId The ID of the conversation to mark as read.
 * @param type The type of conversation ('channel' or 'dm').
 * @returns The new workspace state.
 */
export const applyMarkConversationReadToWorkspace = (
  prevWs: Workspace,
  conversationId: string,
  type: 'channel' | 'dm'
): Workspace => {
  if (type === 'channel') {
    const newSections = prevWs.sections.map(s => ({
      ...s,
      channels: s.channels.map(ch =>
        ch.id === conversationId ? { ...ch, unreadCount: 0, isUnreadCountPlaceholder: false } : ch
      ),
    }));
    return { ...prevWs, sections: newSections };
  } else { // type === 'dm'
    const newDirectMessages = prevWs.directMessages.map(dm =>
      dm.id === conversationId ? { ...dm, unreadCount: 0, isUnreadCountPlaceholder: false } : dm
    );
    return { ...prevWs, directMessages: newDirectMessages };
  }
};

/**
 * Updates the settings for a specific user within the workspace state.
 * @param prevWs The previous workspace state.
 * @param userId The ID of the user whose settings are to be updated.
 * @param newSettings The new settings object for the user.
 * @returns The new workspace state.
 */
export const applyUpdateUserSettingToWorkspace = (
  prevWs: Workspace,
  userId: string,
  newSettings: UserSettings // Assuming UserSettings is imported from './types'
): Workspace => {
  const newUsers = prevWs.users.map(u =>
    u.id === userId ? { ...u, settings: newSettings } : u
  );
  // Note: The mockWorkspaces update is not handled here as it's a side effect specific to the context's current implementation.
  // That part will remain in the context or be handled differently during hook refactoring.
  return { ...prevWs, users: newUsers };
};

/**
 * Updates the general settings for the workspace.
 * @param prevWs The previous workspace state.
 * @param newSettings A partial object of the new workspace settings.
 * @returns The new workspace state.
 */
export const applyUpdateWorkspaceSettingsToWorkspace = (
  prevWs: Workspace,
  newSettings: Partial<WorkspaceSettings> // Assuming WorkspaceSettings is imported
): Workspace => {
  const updatedSettings = { ...prevWs.settings, ...newSettings };
  // Note: The mockWorkspaces update is not handled here.
  return { ...prevWs, settings: updatedSettings };
};

/**
 * Updates the status and status message for a specific user.
 * @param prevWs The previous workspace state.
 * @param userId The ID of the user whose status is to be updated.
 * @param status The new status.
 * @param statusMessage The new status message (optional).
 * @returns The new workspace state.
 */
export const applyUpdateUserStatusToWorkspace = (
  prevWs: Workspace,
  userId: string,
  status: User['status'], // Assuming User is imported
  statusMessage?: string
): Workspace => {
  const newUsers = prevWs.users.map(u =>
    u.id === userId ? { ...u, status, statusMessage } : u
  );
  // Note: The mockWorkspaces update is not handled here.
  return { ...prevWs, users: newUsers };
};

/**
 * Sets the active topic for a specific channel.
 * @param prevWs The previous workspace state.
 * @param channelId The ID of the channel.
 * @param topicId The ID of the topic to set as active.
 * @returns The new workspace state.
 */
export const applyNavigateToChannelTopicToWorkspace = (
  prevWs: Workspace,
  channelId: string,
  topicId: string
): Workspace => {
  const newSections = prevWs.sections.map(s => ({
    ...s,
    channels: s.channels.map(c =>
      c.id === channelId ? { ...c, activeChannelTopicId: topicId } : c
    ),
  }));
  return { ...prevWs, sections: newSections };
};

/**
 * Clears the active topic for a specific channel.
 * @param prevWs The previous workspace state.
 * @param channelId The ID of the channel.
 * @returns The new workspace state.
 */
export const applyClearActiveChannelTopicToWorkspace = (
  prevWs: Workspace,
  channelId: string
): Workspace => {
  const newSections = prevWs.sections.map(s => ({
    ...s,
    channels: s.channels.map(c =>
      c.id === channelId ? { ...c, activeChannelTopicId: undefined } : c
    ),
  }));
  return { ...prevWs, sections: newSections };
};

/**
 * Applies a reaction update to a message within the workspace state.
 * Adds or removes a reaction for a given user and emoji.
 * @param prevWs The previous workspace state.
 * @param messageId The ID of the message to react to.
 * @param emoji The emoji string for the reaction.
 * @param currentAuthUserId The ID of the user adding/removing the reaction.
 * @returns The new workspace state with the reaction update applied.
 */
export const applyReactionUpdateToWorkspace = (
  prevWs: Workspace,
  messageId: string,
  emoji: string,
  currentAuthUserId: string
): Workspace => {
  const newWs = JSON.parse(JSON.stringify(prevWs));

  const processMsgReactions = (msg: Message) => {
    if (msg.id === messageId) {
      msg.reactions_summary = msg.reactions_summary || [];
      let reactionSummary = msg.reactions_summary.find(rs => rs.emoji === emoji);

      if (reactionSummary) {
        // Reaction type exists
        const userReactedIndex = reactionSummary.user_ids_array.indexOf(currentAuthUserId);
        if (userReactedIndex > -1) {
          // User has reacted, so remove reaction
          reactionSummary.count--;
          reactionSummary.user_ids_array.splice(userReactedIndex, 1);
          // If count is 0, remove the reaction summary object
          if (reactionSummary.count === 0) {
            msg.reactions_summary = msg.reactions_summary.filter(rs => rs.emoji !== emoji);
          }
        } else {
          // User has not reacted, so add reaction
          reactionSummary.count++;
          reactionSummary.user_ids_array.push(currentAuthUserId);
        }
      } else {
        // Reaction type does not exist, add new reaction summary
        msg.reactions_summary.push({
          emoji: emoji,
          count: 1,
          user_ids_array: [currentAuthUserId],
          // last_reacted_at is managed by the DB view, not client-side optimistic update
        });
      }
    }
  };

  newWs.sections.forEach((s: Section) =>
    s.channels.forEach((c: Channel) => {
      c.messages.forEach(processMsgReactions);
      Object.values(c.threads).forEach(th => (th as Thread).messages.forEach(processMsgReactions));
    })
  );

  newWs.directMessages.forEach((dm: DirectMessage) => {
    dm.messages.forEach(processMsgReactions);
    Object.values(dm.threads).forEach(th => (th as Thread).messages.forEach(processMsgReactions));
  });

  return newWs;
};
