
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import './styles/keyboard-navigation.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 13% 65%;
    --primary-foreground: 220 13% 25%;

    --secondary: 220 14% 98%;
    --secondary-foreground: 220 13% 45%;

    --muted: 220 14% 98%;
    --muted-foreground: 220 9% 55%;

    --accent: 220 14% 98%;
    --accent-foreground: 220 13% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%; /* Reverted to original */
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Text size scaling variables - default to medium */
    --text-scale: 1;
    --text-xs: calc(0.75rem * var(--text-scale));
    --text-sm: calc(0.875rem * var(--text-scale));
    --text-base: calc(1rem * var(--text-scale));
    --text-lg: calc(1.125rem * var(--text-scale));
    --text-xl: calc(1.25rem * var(--text-scale));
    --text-2xl: calc(1.5rem * var(--text-scale));
    --text-3xl: calc(1.875rem * var(--text-scale));
    --text-4xl: calc(2.25rem * var(--text-scale));

    /* Component size scaling */
    --component-scale: 1;
    --spacing-1: calc(0.25rem * var(--component-scale));
    --spacing-2: calc(0.5rem * var(--component-scale));
    --spacing-3: calc(0.75rem * var(--component-scale));
    --spacing-4: calc(1rem * var(--component-scale));
    --spacing-6: calc(1.5rem * var(--component-scale));
    --spacing-8: calc(2rem * var(--component-scale));
    --spacing-12: calc(3rem * var(--component-scale));

    /* Button and input heights */
    --button-height: calc(2.5rem * var(--component-scale));
    --button-height-sm: calc(2.25rem * var(--component-scale));
    --button-height-lg: calc(2.75rem * var(--component-scale));

    /* Icon sizes */
    --icon-sm: calc(1rem * var(--text-scale));
    --icon-md: calc(1.25rem * var(--text-scale));
    --icon-lg: calc(1.5rem * var(--text-scale));

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* RGB values for app-highlight for use in rgba() */
    --app-highlight-rgb: 59, 130, 246;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%; /* Reverted to original */
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* RGB values for app-highlight for use in rgba() */
    --app-highlight-rgb: 59, 130, 246;
  }
}

@layer base {
  /* Removed global border application:
  * {
    @apply border-border;
  }
  */

  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Text size scaling classes */
  .text-size-small {
    --text-scale: 0.875;
    --component-scale: 0.9;
  }

  .text-size-medium {
    --text-scale: 1;
    --component-scale: 1;
  }

  .text-size-large {
    --text-scale: 1.125;
    --component-scale: 1.1;
  }

  /* Apply scaling to common UI elements */
  button, .btn {
    height: var(--button-height);
    font-size: var(--text-sm);
  }

  button.btn-sm, .btn-sm {
    height: var(--button-height-sm);
    font-size: var(--text-xs);
  }

  button.btn-lg, .btn-lg {
    height: var(--button-height-lg);
    font-size: var(--text-base);
  }

  input, textarea, select {
    height: var(--button-height);
    font-size: var(--text-sm);
  }

  /* Scale text sizes using CSS custom properties */
  .text-xs { font-size: var(--text-xs) !important; }
  .text-sm { font-size: var(--text-sm) !important; }
  .text-base { font-size: var(--text-base) !important; }
  .text-lg { font-size: var(--text-lg) !important; }
  .text-xl { font-size: var(--text-xl) !important; }
  .text-2xl { font-size: var(--text-2xl) !important; }
  .text-3xl { font-size: var(--text-3xl) !important; }
  .text-4xl { font-size: var(--text-4xl) !important; }

  /* Scale icons */
  .icon-sm {
    width: var(--icon-sm);
    height: var(--icon-sm);
  }
  .icon-md {
    width: var(--icon-md);
    height: var(--icon-md);
  }
  .icon-lg {
    width: var(--icon-lg);
    height: var(--icon-lg);
  }

  /* Scale spacing */
  .p-1 { padding: var(--spacing-1) !important; }
  .p-2 { padding: var(--spacing-2) !important; }
  .p-3 { padding: var(--spacing-3) !important; }
  .p-4 { padding: var(--spacing-4) !important; }
  .p-6 { padding: var(--spacing-6) !important; }
  .p-8 { padding: var(--spacing-8) !important; }

  .m-1 { margin: var(--spacing-1) !important; }
  .m-2 { margin: var(--spacing-2) !important; }
  .m-3 { margin: var(--spacing-3) !important; }
  .m-4 { margin: var(--spacing-4) !important; }
  .m-6 { margin: var(--spacing-6) !important; }
  .m-8 { margin: var(--spacing-8) !important; }
}

.app-sidebar {
  @apply bg-app-sidebar text-app-text;
  /* The @apply above should handle setting background to var(--app-sidebar)
     and text to var(--app-text) based on tailwind.config.ts definitions.
     The explicit line below with a fallback was likely causing issues or overriding. */
}

.channel-hover:hover {
  @apply bg-app-hover;
}

.thread-border {
  @apply border-l border-border pl-4 ml-6 mt-1; /* Changed to 1px and use theme border */
}

.message-input {
  @apply border border-border rounded-md p-2 w-full focus:outline-none focus:ring-2 focus:ring-app-active;
}

.emoji-picker {
  @apply absolute bottom-full mb-2 bg-white shadow-lg rounded-lg p-2 z-10;
}
