import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useApp } from '@/lib/app-context';
import { Send, Plus, Smile, AtSign, Edit3, Maximize2, Minimize2 } from 'lucide-react';
import SimpleMDEEditor from 'react-simplemde-editor';
import 'easymde/dist/easymde.min.css';
import type { Options } from 'easymde';
import EasyMDE from 'easymde';
import { LocalAttachment } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';
import { AttachmentPill } from './AttachmentPill';
import { EmojiPicker } from './EmojiPicker';
import { Button } from './ui/button';

interface MessageInputProps {
  threadId?: string;
  placeholder?: string;
  autoFocus?: boolean;
  topicId?: string;
  disabled?: boolean;
}

export const MessageInput = ({
  threadId,
  placeholder = "Type a message...",
  autoFocus = false,
  topicId,
  disabled = false
}: MessageInputProps) => {
  const [message, setMessage] = useState('');
  const [pendingAttachments, setPendingAttachments] = useState<LocalAttachment[]>([]);
  const [showMarkdownToolbar, setShowMarkdownToolbar] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const { sendMessage, currentChannel, currentDirectMessage, workspaceSettings } = useApp();
  const editorInstanceRef = useRef<EasyMDE | null>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateImageName = (file: File): string => {
    // If the file has a meaningful name (not generic), use it
    if (file.name && file.name !== 'image.png' && file.name !== 'image' && !file.name.startsWith('blob:')) {
      return file.name;
    }

    // Generate a better name for pasted/generic images
    const now = new Date();
    const timeStr = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/:/g, '-');
    const dateStr = now.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
    const extension = file.type.split('/')[1] || 'png';
    return `Screenshot ${dateStr} at ${timeStr}.${extension}`;
  };

  const addFileAsAttachment = async (file: File) => {
    const localId = uuidv4();
    const fileName = file.type.startsWith('image/') ? generateImageName(file) : file.name;

    const newAttachmentBase: Omit<LocalAttachment, 'dataUrl' | 'textContent'> = {
      id: localId,
      name: fileName,
      type: file.type,
      size: file.size,
      fileObject: file,
    };

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, dataUrl: e.target?.result as string }]);
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('text/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, textContent: e.target?.result as string }]);
      };
      reader.readAsText(file);
    } else {
      console.warn(`File type ${file.type} not directly supported for inline content. Will be handled by upload later.`);
      setPendingAttachments(prev => [...prev, { ...newAttachmentBase }]);
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setPendingAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const renameAttachment = (attachmentId: string, newName: string) => {
    setPendingAttachments(prev =>
      prev.map(att =>
        att.id === attachmentId ? { ...att, name: newName } : att
      )
    );
  };

  const handleSendMessage = () => {
    if ((!message.trim() && pendingAttachments.length === 0) || disabled) return;

    if (topicId) {
      sendMessage(message, currentChannel?.id, undefined, threadId, topicId, pendingAttachments);
    } else {
      sendMessage(message, currentChannel?.id, currentDirectMessage?.id, threadId, undefined, pendingAttachments);
    }
    setMessage('');
    setPendingAttachments([]);
    editorInstanceRef.current?.codemirror.focus();
  };

  const handleEditorKeyDown = (_instance: any, event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      if (isExpanded) {
        setIsExpanded(false);
      } else {
        editorInstanceRef.current?.codemirror.getInputField().blur();
      }
    }
  };

  const handlePaste = (_instance: any, event: ClipboardEvent) => {
    if (event.clipboardData) {
      for (let i = 0; i < event.clipboardData.items.length; i++) {
        const item = event.clipboardData.items[i];
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            event.preventDefault();
            addFileAsAttachment(file);
          }
        }
      }
    }
  };

  const simpleMdeOptions: Options = useMemo(() => {
    return {
      autofocus: autoFocus,
      placeholder: disabled ? "Cannot send messages to an archived topic" : placeholder,
      toolbar: showMarkdownToolbar ? [
        'bold', 'italic', 'strikethrough', '|',
        'heading', 'heading-smaller', 'heading-bigger', '|',
        'code', 'quote', '|',
        'unordered-list', 'ordered-list', '|',
        'link', 'image', 'table', '|',
        'horizontal-rule', 'preview'
      ] : false,
      status: false,
      spellChecker: false,
      minHeight: isExpanded ? "calc(100vh - 200px)" : "40px",
      maxHeight: isExpanded ? "calc(100vh - 200px)" : undefined,
      // Disable conflicting shortcuts to prevent interference with global app shortcuts
      shortcuts: {
        toggleBold: null,
        toggleItalic: null,
        toggleStrikethrough: null,
        toggleCodeBlock: null,
        toggleHeadingSmaller: null,
        toggleHeadingBigger: null,
        toggleHeading1: null,
        toggleHeading2: null,
        toggleHeading3: null,
        toggleUnorderedList: null,
        toggleOrderedList: null,
        cleanBlock: null,
        drawLink: null,
        drawImage: null,
        drawTable: null,
        drawHorizontalRule: null,
        undo: null,
        redo: null,
        togglePreview: null,
        toggleSideBySide: null,
        toggleFullScreen: null
      }
    };
  }, [autoFocus, placeholder, disabled, showMarkdownToolbar, isExpanded]);

  useEffect(() => {
    if (autoFocus && editorInstanceRef.current) {
      setTimeout(() => editorInstanceRef.current?.codemirror.focus(), 0);
    }
  }, [autoFocus, currentChannel, currentDirectMessage]);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled) return;

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      Array.from(event.dataTransfer.files).forEach(file => {
        addFileAsAttachment(file);
      });
      event.dataTransfer.clearData();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      Array.from(event.target.files).forEach(file => {
        addFileAsAttachment(file);
      });
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    if (editorInstanceRef.current) {
      const cm = editorInstanceRef.current.codemirror;
      const cursor = cm.getCursor();
      cm.replaceRange(emoji, cursor);
      cm.focus();
    }
  };

  const handleMentionClick = () => {
    if (editorInstanceRef.current) {
      const cm = editorInstanceRef.current.codemirror;
      const cursor = cm.getCursor();
      cm.replaceRange('@', cursor);
      cm.focus();
    }
  };

  const toggleMarkdownToolbar = () => {
    setShowMarkdownToolbar(!showMarkdownToolbar);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    // Focus the editor after state change
    setTimeout(() => {
      if (editorInstanceRef.current) {
        editorInstanceRef.current.codemirror.focus();
      }
    }, 100);
  };

  return (
    <>
      {/* Backdrop for expanded mode */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsExpanded(false)}
        />
      )}

      <div
        ref={dropZoneRef}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className={`flex flex-col border border-[var(--app-border)] rounded-lg bg-[var(--app-main-bg)] transition-all duration-200 ${
          isExpanded ? 'fixed inset-4 z-50 h-auto shadow-2xl' : 'relative shadow-sm'
        }`}
      >
      {/* Attachments area - only show when there are attachments */}
      {pendingAttachments.length > 0 && (
        <div className="px-3 py-2 border-b border-[var(--app-border)] flex flex-wrap gap-2">
          {pendingAttachments.map(att => (
            <AttachmentPill key={att.id} attachment={att} onRemove={removeAttachment} onRename={renameAttachment} />
          ))}
        </div>
      )}

      {/* Main editor area */}
      <div className={`message-input-editor-wrapper ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        <SimpleMDEEditor
          value={message}
          onChange={setMessage}
          options={simpleMdeOptions}
          getMdeInstance={(instance) => {
            editorInstanceRef.current = instance;
          }}
          events={{
            keydown: handleEditorKeyDown,
            paste: handlePaste,
          }}
        />
      </div>

      {/* Bottom toolbar */}
      <div className="flex items-center px-3 py-2 border-t border-[var(--app-border)] bg-[var(--app-main-bg)]">
        <input
          type="file"
          multiple
          ref={fileInputRef}
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,text/plain,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar"
        />

        {/* Left side buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
            title="Attach file"
          >
            <Plus size={16} />
          </Button>

          <EmojiPicker
            onEmojiSelect={handleEmojiSelect}
            workspaceSettings={workspaceSettings}
            open={emojiPickerOpen}
            onOpenChange={setEmojiPickerOpen}
          >
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
              disabled={disabled}
              title="Add emoji"
            >
              <Smile size={16} />
            </Button>
          </EmojiPicker>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
            onClick={handleMentionClick}
            disabled={disabled}
            title="Mention someone"
          >
            <AtSign size={16} />
          </Button>
        </div>

        {/* Center spacer */}
        <div className="flex-grow" />

        {/* Right side buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)] ${showMarkdownToolbar ? 'bg-[var(--app-hover-bg)]' : ''}`}
            onClick={toggleMarkdownToolbar}
            disabled={disabled}
            title="Toggle markdown toolbar"
          >
            <Edit3 size={16} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
            onClick={toggleExpanded}
            disabled={disabled}
            title={isExpanded ? "Minimize" : "Expand"}
          >
            {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 transition-colors ${
              (message.trim() || pendingAttachments.length > 0) && !disabled
                ? 'text-[var(--app-highlight)] hover:text-[var(--app-active)] hover:bg-[var(--app-hover-bg)]'
                : 'text-[var(--app-main-text)] opacity-30'
            } ${disabled ? 'cursor-not-allowed' : ''}`}
            onClick={handleSendMessage}
            disabled={(!message.trim() && pendingAttachments.length === 0) || disabled}
            title="Send message"
          >
            <Send size={16} />
          </Button>
        </div>
      </div>
      </div>
    </>
  );
};
