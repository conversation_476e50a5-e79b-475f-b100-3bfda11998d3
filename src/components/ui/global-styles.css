/* Global styles for the app */

/* Disable text selection for sidebar */
.app-sidebar {
  user-select: none;
  background: var(--app-sidebar);
  color: var(--app-text);
  min-width: 200px; /* Minimum width */
  position: relative;
}

/* Resize handle styles */
.app-sidebar .resize-handle {
  position: absolute;
  right: -4px;
  top: 0;
  bottom: 0;
  width: 8px;
  cursor: col-resize;
  opacity: 0;
  transition: opacity 0.2s;
  background-color: transparent;
  z-index: 10;
}

.app-sidebar .resize-handle:hover,
.app-sidebar .resize-handle:active {
  opacity: 1;
}

.app-sidebar .resize-handle::after {
  content: "";
  position: absolute;
  top: 0;
  right: 3px;
  bottom: 0;
  width: 2px;
  background-color: var(--app-border);
  opacity: 0;
  transition: opacity 0.2s;
}

.app-sidebar .resize-handle:hover::after,
.app-sidebar .resize-handle:active::after {
  opacity: 1;
}

/* Styles for the sidebar header (workspace name area) */
.app-sidebar-header {
  border-bottom: 1px solid var(--app-border);
  /* Default hover, can be overridden by specific theme variables if needed */
}

.app-sidebar-header:hover {
  background-color: var(--app-hover-bg);
}

.app-sidebar-header .workspace-icon-bg {
  background-color: var(--app-highlight);
  color: var(--app-sidebar);
}

.app-sidebar-header .workspace-name {
  color: var(--app-highlight);
  font-weight: bold;
}

.app-main-content {
  background: var(--app-main-bg);
  color: var(--app-main-text);
}

/* Main header styling */
.app-main-content .main-header {
  background: var(--app-sidebar);
  border-bottom: 1px solid var(--app-border);
  color: var(--app-text);
}

.app-main-content .main-header button {
  color: var(--app-text);
}

.app-main-content .main-header button:hover {
  background-color: var(--app-hover-bg);
}

/* Global text color styles for main content area */

/* Channel name and description */
.app-main-content .channel-name {
  color: var(--app-main-text);
}

.app-main-content .channel-description {
  color: var(--app-main-text);
  opacity: 0.8;
}

/* Tab text colors */
.app-main-content .tab-text {
  color: var(--app-main-text);
  opacity: 0.8;
}

.app-main-content .tab-text[data-state="active"] {
  color: var(--app-main-text);
  opacity: 1;
}

/* Topic and file names */
.app-main-content .topic-name,
.app-main-content .file-name {
  color: var(--app-main-text);
}

.app-main-content .topic-description,
.app-main-content .file-info {
  color: var(--app-main-text);
  opacity: 0.7;
}

/* Date dividers and metadata */
.app-main-content .date-divider,
.app-main-content .metadata {
  color: var(--app-main-text);
  opacity: 0.6;
}

/* Message content */
.app-main-content .message-content {
  color: var(--app-main-text);
}

/* Section headers */
.app-main-content .section-header {
  color: var(--app-main-text);
  opacity: 0.9;
}

/* Enable text selection for chat content */
.user-select-text {
  user-select: text;
}

/* Hover effect for channels with consistent spacing */
.channel-hover:hover {
  background-color: var(--app-hover-bg); /* Use theme variable */
  transition: background-color 0.1s ease-in-out;
}

/* Theme colors */
/* :root will now primarily hold shadcn/ui compatible vars, defined in index.css */
/* Default app theme variables will be under .theme-default */

body.theme-default {
  /* Clean, professional light theme with neutral grays */
  --app-sidebar: #F8F9FA; /* Very light gray for calm sidebar */
  --app-border: #E5E7EB; /* Subtle border color */
  --app-text: #374151; /* Dark gray for good readability */
  --app-highlight: #64748B; /* Slightly darker blue-gray for better visibility */
  --app-active: var(--app-selected-item); /* Active state, now matches selected item */
  --app-active-text: #111827; /* Near black text for active items */
  --app-thread-highlight: rgba(75, 85, 99, 0.08); /* Very subtle gray highlight */
  --app-hover-bg: rgba(0, 0, 0, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #E5E7EB; /* Subtle gray selection - Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(0, 0, 0, 0.03);
  --app-main-bg: #ffffff;
  --app-main-text: #1F2937;
  --primary: 220 17% 90%; /* Light grey for buttons */
  --primary-foreground: 215 14% 34%; /* Dark grey text for buttons */
}

/* Selected channel/item highlighting */
.selected-item {
  background-color: var(--app-selected-item) !important;
}

/* Thread message highlighting */
.thread-active-message {
  background-color: var(--app-thread-highlight) !important;
}

/* Project spacing in sidebar - ensure consistent height to prevent jumping */
.project-item {
  /* margin-bottom: 4px; Removed to use space-y on ul */
  /* Adding padding to the clickable div inside project-item for consistent height */
}
.project-item > div:first-child { /* Targeting the clickable project header */
  padding-top: 2px;
  padding-bottom: 2px;
}

/* Fix alignment for hover actions to prevent layout shift */
.project-hover-actions {
  width: 24px; /* Fixed width */
  height: 24px; /* Fixed height */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent shrinking */
  /* Opacity and pointer-events are handled by Tailwind in Sidebar.tsx */
}

/* Message styles */
.app-main-content .message {
  color: var(--app-main-text);
}

.app-main-content .message:hover {
  background-color: var(--app-hover-bg);
}

.app-main-content .message-sender {
  color: var(--app-main-text);
}

.app-main-content .message-timestamp {
  color: var(--app-main-text);
  opacity: 0.6;
}

.app-main-content .message-content {
  color: var(--app-main-text);
}

/* Thread parent and quote styling */
.thread-parent-message {
  border-left: 3px solid var(--app-highlight);
  padding-left: 8px;
  background-color: var(--app-hover-bg);
}

.thread-quote {
  position: relative;
  padding: 8px 12px;
  background-color: var(--app-hover-bg);
  border-left: 2px solid var(--app-highlight); /* Reduced border width */
  margin: 8px 0;
  font-size: 0.95em;
  color: var(--app-main-text);
  border-radius: 4px;
}

/* Better styling for thread header */
.thread-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--app-border);
  background-color: #fff; /* Consider theme variable */
}
.dark .thread-header {
  background-color: var(--app-sidebar); /* Match sidebar for dark themes often */
}

/* User section at bottom of sidebar */
.user-section {
  background-color: var(--app-user-section);
}

.user-section:hover {
  /* Fallback to --app-hover-bg if --app-user-section-hover is not defined by the theme */
  background-color: var(--app-user-section-hover, var(--app-hover-bg));
}

/* Theme classes */
body.theme-dark {
  --app-sidebar: #1a1d21;
  --app-border: #383838;
  --app-text: #d1d2d3;
  --app-highlight: #1164a3; /* Keeping consistent unless theme demands change */
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(255, 245, 204, 0.2);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #1164a3; /* Now using original active color */
  --app-user-section: transparent; /* Match sidebar */
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  /* Assuming a dark theme might want a dark main background too */
  --app-main-bg: #121212; /* Example dark main bg */
  --app-main-text: #e0e0e0; /* Example light text for dark bg */
  --primary: 207 82% 35%; /* From --app-active */
  --primary-foreground: 0 0% 100%; /* White */
}

body.theme-royal-purple { /* Was theme-purple */
  --app-sidebar: #4a154b;
  --app-border: #EAE4EB; /* Even lighter for more subtlety */
  --app-text: #d1d2d3;
  --app-highlight: #D8BFD8; /* Light Thistle/Lavender for accent */
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(245, 225, 245, 0.32); /* Tuned down: Lighter base, slightly reduced opacity */
  --app-hover-bg: rgba(74, 21, 75, 0.02); /* Greatly reduced hover effect */
  --app-selected-item: #8a3ab9; /* Now using original active color */
  --app-user-section: transparent; /* Slightly darker than sidebar */
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  --app-main-bg: #fbf8fb;
  --app-main-text: #1d1c1d;
  --primary: 298 55% 19%; /* From --app-sidebar */
  --primary-foreground: 220 3% 82%; /* From --app-text */
}

body.theme-forest-green { /* Was theme-green */
  --app-sidebar: #0e3d2d;
  --app-border: #DCEEE6; /* Light, desaturated green for subtlety */
  --app-text: #e5ecef;
  --app-main-bg: #f0f5f3;
  --app-main-text: #1d1c1d;
  --app-highlight: #B0E0B8;
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(176, 224, 184, 0.25); /* Based on --app-highlight */
  --app-hover-bg: rgba(14, 61, 45, 0.02); /* Greatly reduced hover effect */
  --app-selected-item: #1c9a62; /* Now using original active color */
  --app-user-section: transparent; /* Slightly darker than sidebar */
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  --primary: 160 62% 15%; /* From --app-sidebar */
  --primary-foreground: 200 23% 92%; /* From --app-text */
}

body.theme-sunset-orange { /* Was theme-orange */
  --app-sidebar: #e05d11;
  --app-border: #FFEFE5; /* Light, desaturated orange for subtlety */
  --app-text: #FFEFE5; /* Using border color for subtle off-white */
  --app-main-bg: #fff8f2;
  --app-main-text: #1d1c1d;
  --app-highlight: #FFDAB9; /* Light Peach for accent */
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(255, 220, 200, 0.28); /* Adjusted base and opacity */
  --app-hover-bg: rgba(224, 93, 17, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #bf4c08; /* Now using original active color */
  --app-user-section: transparent; /* Slightly darker than sidebar */
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  --primary: 25 87% 47%; /* From --app-sidebar */
  --primary-foreground: 0 0% 100%; /* From --app-text */
}

body.theme-ocean-teal { /* Was theme-jade */
  --app-sidebar: #0e6e5c;
  --app-border: #E0F2F0; /* Light, desaturated teal for subtlety */
  --app-text: #E0F2F0; /* Using border color for subtle off-white */
  --app-main-bg: #eff9f7;
--primary: 169 79% 24%; /* From --app-sidebar */
  --primary-foreground: 0 0% 100%; /* From --app-text */
  --app-main-text: #1d1c1d;
  --app-highlight: #20c4aa;
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(32, 196, 170, 0.22); /* Based on --app-highlight */
  --app-hover-bg: rgba(14, 110, 92, 0.02); /* Greatly reduced hover effect */
  --app-selected-item: #09574a; /* Now using original active color */
  --app-user-section: transparent; /* Slightly darker than sidebar */
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
}

body.theme-midnight-blue { /* Corresponds to 'midnight-blue' in ThemeSelectionGrid */
  --app-sidebar: #1d2252;
  --app-border: #2a316e;
  --app-text: #ffffff; /* General text for this theme */
  --app-highlight: #a3a6ff; /* Brighter highlight */
  --app-active: var(--app-selected-item); /* Active state, now matches selected item */
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(220, 225, 255, 0.3);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #2a3178; /* Better selection contrast - Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.08);
  --app-main-bg: #10132b;
  --app-main-text: #ffffff; /* Pure white text for maximum contrast */
  --primary: 240 64% 22%;
  --primary-foreground: 0 0% 100%;
}

body.theme-graphite { /* Added based on ThemeSelectionGrid */
  --app-sidebar: #383838;
  --app-border: #505050; /* Guessed border */
  --app-text: #e0e0e0; /* General text */
  --app-highlight: #ffffff; /* Brighter highlight - Changed to white */
  --app-active: var(--app-selected-item); /* Active state, now matches selected item */
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(200, 200, 200, 0.2);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #4a4a4a; /* Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.08);
  --app-main-bg: #2a2a2a; /* From ThemeSelectionGrid */
  --app-main-text: #ffffff; /* Pure white text for maximum contrast */
  --primary: 0 0% 22%;
  --primary-foreground: 0 0% 100%;
}


/* Gradient themes - Refined implementations */
body.theme-gradient-aqua-dream {
  --app-sidebar: linear-gradient(165deg,
    rgba(105, 210, 231, 0.98),
    rgba(167, 219, 216, 0.99));
  --app-border: rgba(167, 219, 216, 0.4);
  --app-text: #1d1c1d;
  --app-highlight: #1d1c1d;
  --app-active: var(--app-selected-item);
  --app-active-text: #1d1c1d;
  --app-thread-highlight: rgba(105, 210, 231, 0.2);
  --app-hover-bg: rgba(105, 210, 231, 0.02); /* Greatly reduced hover effect */
  --app-selected-item: rgba(105, 210, 231, 0.95); /* Darker selection for better contrast - Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(0, 0, 0, 0.05);
  --app-main-bg: linear-gradient(to bottom,
    rgba(167, 219, 216, 0.08),
    rgba(188, 226, 232, 0.12));
  --app-main-text: #1d1c1d;
  --primary: 193 72% 66%;
  --primary-foreground: 300 2% 11%;
}

body.theme-gradient-sky-blue {
  --app-sidebar: linear-gradient(165deg,
    rgba(76, 159, 213, 0.98),
    rgba(118, 205, 234, 0.99));
  --app-border: rgba(118, 205, 234, 0.4);
  --app-text: #ffffff;
  --app-highlight: #ffffff;
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(76, 159, 213, 0.2);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: rgba(76, 159, 213, 0.95); /* Increased opacity for better contrast - Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  --app-main-bg: linear-gradient(to bottom,
    rgba(118, 205, 234, 0.08),
    rgba(160, 216, 239, 0.12));
  --app-main-text: #1d1c1d;
  --primary: 205 62% 57%;
  --primary-foreground: 0 0% 100%;
}

body.theme-gradient-deep-ocean {
  --app-sidebar: linear-gradient(165deg,
    rgba(29, 43, 83, 0.98),
    rgba(48, 64, 128, 0.99));
  --app-border: rgba(126, 156, 224, 0.2);
  --app-text: #ffffff;
  --app-highlight: #a3b8ff;
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(126, 156, 224, 0.15);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: rgba(126, 156, 224, 0.95); /* Now using original active color */
  --app-user-section: rgba(29, 43, 83, 0.95);
  --app-user-section-hover: rgba(255, 255, 255, 0.08);
  --app-main-bg: #1a1f36;
  --app-main-text: #e6e9f0;
  --primary: 230 60% 56%;
  --primary-foreground: 0 0% 100%;
  color-scheme: dark;
}

/* Additional dark mode specific styles for Deep Ocean */
body.theme-gradient-deep-ocean .app-main-content {
  background-color: var(--app-main-bg);
  color: var(--app-main-text);
}

body.theme-gradient-deep-ocean .app-main-content .tab-text[data-state="active"] {
  color: #ffffff;
  opacity: 1;
}

/* File view specific styles for Deep Ocean */
body.theme-gradient-deep-ocean .app-main-content [role="tabpanel"] {
  background-color: var(--app-main-bg);
  color: var(--app-main-text);
}

/* Thread panel specific styles for Deep Ocean */
body.theme-gradient-deep-ocean .thread-header {
  background-color: #1e2440;
  border-color: rgba(126, 156, 224, 0.2);
}

body.theme-gradient-deep-ocean .thread-parent-message {
  background-color: rgba(255, 255, 255, 0.03);
  border-left-color: var(--app-highlight);
}

/* Additional UI elements for dark mode */
body.theme-gradient-deep-ocean .app-main-content input,
body.theme-gradient-deep-ocean .app-main-content textarea {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--app-main-text);
}

body.theme-gradient-deep-ocean .app-main-content input::placeholder,
body.theme-gradient-deep-ocean .app-main-content textarea::placeholder {
  color: rgba(230, 233, 240, 0.5);
}

/* Styles for SimpleMDE to ensure proper height and scrolling */
/* The .EasyMDEContainer is the root div created by SimpleMDE, which gets the className from the component prop */
/* It's already set to flex flex-col flex-grow min-h-0 via ChannelNote.tsx */

.editor-toolbar {
  flex-shrink: 0; /* Prevent toolbar from shrinking */
  position: relative; /* Keep it in flow, ensure z-index works if needed for dropdowns */
  z-index: 10; /* Ensure toolbar dropdowns are above CodeMirror */
  border: none !important;
  background: transparent !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid var(--app-border) !important;
}

.editor-toolbar button {
  background: transparent !important;
  border: none !important;
  color: var(--app-main-text) !important;
  border-radius: 4px !important;
  padding: 6px 8px !important;
  margin: 0 2px !important;
  transition: all 0.15s ease !important;
}

.editor-toolbar button:hover {
  background: var(--app-hover-bg) !important;
  color: var(--app-main-text) !important;
}

.editor-toolbar button.active {
  background: var(--app-hover-bg) !important;
  color: var(--app-highlight) !important;
}

.editor-toolbar i.separator {
  border-left: 1px solid var(--app-border) !important;
  margin: 0 6px !important;
}

/* This is the direct child of .EasyMDEContainer that wraps CodeMirror itself */
.CodeMirror-wrap {
  flex-grow: 1; /* Take remaining space from .EasyMDEContainer (which is flex-col) */
  display: flex; /* Make it a flex container for .CodeMirror */
  flex-direction: column; /* Stack .CodeMirror vertically */
  min-height: 0; /* Crucial for nested flex scrolling */
  position: relative; /* For CodeMirror's absolute positioned elements */
  border: none !important;
}

.CodeMirror {
  flex-grow: 1; /* Grow to fill .CodeMirror-wrap */
  height: 100% !important; /* Fill its parent */
  min-height: 0; /* Allow it to shrink and enable internal scrolling */
  position: relative; /* Ensure its children are positioned correctly */
  background: transparent !important;
  border: none !important;
  font-family: inherit !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: var(--app-main-text) !important;
}

.CodeMirror-scroll {
  overflow-y: auto !important; /* Ensure this is the scrollable part */
  height: 100%; /* Fill the .CodeMirror parent */
  min-height: 0; /* Allow shrinking */
  position: relative; /* For internal elements like cursors */
  padding: 6px 12px !important;
}

.CodeMirror-lines {
  padding: 0 !important;
}

.CodeMirror-line {
  padding: 0 !important;
}

.CodeMirror-cursor {
  border-left: 2px solid var(--app-highlight) !important;
}

.CodeMirror-selected {
  background: var(--app-hover-bg) !important;
}

.CodeMirror-placeholder {
  color: var(--app-main-text) !important;
  opacity: 0.5 !important;
}

/* Optional: Ensure the gutter doesn't cause issues, though usually handled by CodeMirror */
.CodeMirror-gutters {
  height: 100% !important;
  background: transparent !important;
  border: none !important;
}

/* Message input specific styling */
.message-input-editor-wrapper .CodeMirror {
  min-height: 20px !important;
  max-height: 150px !important;
  resize: none !important;
}

.message-input-editor-wrapper .CodeMirror-scroll {
  min-height: 20px !important;
  max-height: 150px !important;
  padding: 6px 12px !important;
}

/* Expanded mode styling */
.message-input-editor-wrapper.expanded .CodeMirror {
  min-height: 300px !important;
  max-height: 80vh !important;
}

.message-input-editor-wrapper.expanded .CodeMirror-scroll {
  min-height: 300px !important;
  max-height: 80vh !important;
}

/* Focus styling for message input */
.message-input-editor-wrapper .CodeMirror-focused {
  outline: none !important;
  box-shadow: none !important;
}

/* Slack-like message input container focus */
.message-input-container:focus-within {
  border-color: var(--app-highlight) !important;
  box-shadow: 0 0 0 1px var(--app-highlight) !important;
}

/* Smooth transitions */
.message-input-container {
  transition: border-color 0.15s ease, box-shadow 0.15s ease !important;
}

/* Toolbar button improvements */
.message-input-toolbar-btn {
  transition: all 0.15s ease !important;
  border-radius: 6px !important;
}

.message-input-toolbar-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Send button special styling */
.message-input-send-btn {
  transition: all 0.2s ease !important;
}

.message-input-send-btn:hover:not(:disabled) {
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.message-input-send-btn:active:not(:disabled) {
  transform: scale(0.95) !important;
}

/* Attachment area styling */
.message-input-attachments {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Toolbar animation */
.editor-toolbar {
  animation: slideDown 0.15s ease-out;
}

/* Expanded mode backdrop */
.message-input-backdrop {
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, 0.1);
}

/* Improved attachment pills in message input */
.message-input-container .attachment-pill {
  background: var(--app-hover-bg) !important;
  border: 1px solid var(--app-border) !important;
  border-radius: 8px !important;
  transition: all 0.15s ease !important;
}

.message-input-container .attachment-pill:hover {
  background: var(--app-selected-item) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure :active state for sidebar items uses the intended selection color */
.project-item > div:first-child:active,
.channel-hover:active {
  background-color: var(--app-selected-item) !important;
}
