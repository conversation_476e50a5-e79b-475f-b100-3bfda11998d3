import React from 'react';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { cn } from "@/lib/utils";
import { Check } from 'lucide-react';
import { TextSize } from '@/lib/types';

interface ThemeSelectionGridProps {
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
  selectedTextSize?: TextSize;
  onTextSizeChange?: (textSize: TextSize) => void;
}

interface ThemePreviewProps {
  id: string;
  value: string;
  label: string;
  sidebarBg?: string;
  sidebarGradient?: string;
  sidebarText: string;
  mainBg?: string;
  mainGradient?: string;
  mainText?: string;
  selectedLabelBg?: string;
  selectedLabelText?: string;
}

const ThemePreview: React.FC<ThemePreviewProps & { selectedTheme: string; onThemeChange: (theme: string) => void; }> = ({
  id,
  value,
  label,
  sidebarBg,
  sidebarGradient,
  sidebarText,
  mainBg,
  mainGradient,
  mainText = '#1d1c1d',
  selectedLabelBg, // New prop
  selectedLabelText, // New prop
  selectedTheme,
  onThemeChange
}) => {
  let effectiveMainText = mainText;
  if (mainBg && mainText === '#1d1c1d') {
    const isMainBgDark = mainBg.startsWith('#') && parseInt(mainBg.substring(1, 3), 16) * 0.299 + parseInt(mainBg.substring(3, 5), 16) * 0.587 + parseInt(mainBg.substring(5, 7), 16) * 0.114 < 128;
    effectiveMainText = isMainBgDark ? '#f0f0f0' : mainText;
  }

  const sidebarStyle: React.CSSProperties = {
    color: sidebarText,
  };
  if (sidebarGradient) {
    sidebarStyle.backgroundImage = sidebarGradient;
  } else if (sidebarBg) {
    sidebarStyle.backgroundColor = sidebarBg;
  }

  const mainStyle: React.CSSProperties = {
    color: effectiveMainText,
  };
  if (mainBg) { // Always apply mainBg if it's defined
    mainStyle.backgroundColor = mainBg;
  }
  if (mainGradient) { // Apply mainGradient, will layer over backgroundColor if transparent
    mainStyle.backgroundImage = mainGradient;
  }

  const isSelected = selectedTheme === value;

  return (
    <button
      onClick={() => onThemeChange(value)}
      className={cn(
        "group relative w-full text-left transition-all duration-200",
        "rounded-lg overflow-hidden border-2",
        isSelected
          ? "border-primary shadow-md scale-[0.98]"
          : "border-transparent hover:border-muted hover:scale-[1.02]",
        "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring"
      )}
    >
      <div className="relative h-20">
        <div className="absolute inset-0 flex">
          <div
            className="w-1/3 h-full flex flex-col items-center justify-center p-1"
            style={sidebarStyle}
          >
            <span className="text-xs font-medium opacity-80">Sidebar</span>
            <div className="w-full h-1 bg-current opacity-30 my-0.5 rounded-sm"></div>
            <div className="w-4/5 h-1 bg-current opacity-20 rounded-sm"></div>
          </div>
          <div
            className="w-2/3 h-full flex flex-col items-start justify-start p-2"
            style={mainStyle}
          >
            <span className="text-xs font-medium opacity-80">Main Content</span>
            <div className="w-full h-1.5 bg-current opacity-10 my-1 rounded-sm"></div>
            <div className="w-3/4 h-1.5 bg-current opacity-10 rounded-sm"></div>
          </div>
        </div>
      </div>
      <div
        className={cn(
          "px-3 py-2 text-sm font-medium flex items-center justify-between",
          !isSelected && "bg-muted/50 hover:bg-muted/80" // Apply non-selected style only
        )}
        style={isSelected ? { backgroundColor: selectedLabelBg || sidebarBg, color: selectedLabelText || sidebarText } : {}}
      >
        <span>{label}</span>
        {isSelected && (
          <Check
            className="w-4 h-4 animate-in fade-in duration-200"
            style={{ color: selectedLabelText || sidebarText }}
          />
        )}
      </div>
    </button>
  );
};

export const ThemeSelectionGrid: React.FC<ThemeSelectionGridProps> = ({
  selectedTheme,
  onThemeChange,
  selectedTextSize = 'medium',
  onTextSizeChange,
}) => {
  const singleColorThemes: ThemePreviewProps[] = [
    { id: 'theme-royal-purple', value: 'royal-purple', label: 'Royal Purple', sidebarBg: '#4a154b', sidebarText: '#d1d2d3', mainBg: '#f5f0f5', mainText: '#1d1c1d', selectedLabelBg: '#4a154b', selectedLabelText: '#d1d2d3' },
    { id: 'theme-midnight-blue', value: 'midnight-blue', label: 'Midnight Blue', sidebarBg: '#1d2252', sidebarText: '#ffffff', mainBg: '#10132b', mainText: '#e0e0e0', selectedLabelBg: '#1d2252', selectedLabelText: '#ffffff' },
    { id: 'theme-forest-green', value: 'forest-green', label: 'Forest Green', sidebarBg: '#0e3d2d', sidebarText: '#e5ecef', mainBg: '#f0f5f3', mainText: '#1d1c1d', selectedLabelBg: '#0e3d2d', selectedLabelText: '#e5ecef' },
    { id: 'theme-sunset-orange', value: 'sunset-orange', label: 'Sunset Orange', sidebarBg: '#e05d11', sidebarText: '#ffffff', mainBg: '#fff8f2', mainText: '#1d1c1d', selectedLabelBg: '#e05d11', selectedLabelText: '#ffffff' },
    { id: 'theme-default-light', value: 'default', label: 'Default Light', sidebarBg: '#F8F9FA', sidebarText: '#374151', mainBg: '#ffffff', mainText: '#1F2937', selectedLabelBg: '#E5E7EB', selectedLabelText: '#111827' },
    { id: 'theme-graphite', value: 'graphite', label: 'Graphite', sidebarBg: '#383838', sidebarText: '#e0e0e0', mainBg: '#2a2a2a', mainText: '#d1d2d3', selectedLabelBg: '#383838', selectedLabelText: '#e0e0e0' },
  ];

  const gradientThemes: ThemePreviewProps[] = [
    {
      id: 'theme-gradient-aqua-dream',
      value: 'gradient-aqua-dream',
      label: 'Aqua Dream',
      sidebarGradient: 'linear-gradient(165deg, rgba(105, 210, 231, 0.98), rgba(167, 219, 216, 0.99))',
      sidebarText: '#1d1c1d',
      mainBg: '#ffffff',
      mainGradient: 'linear-gradient(to bottom, rgba(167, 219, 216, 0.08), rgba(188, 226, 232, 0.12))',
      mainText: '#1d1c1d',
      selectedLabelBg: 'rgb(105, 210, 231)',
      selectedLabelText: '#1d1c1d'
    },
    {
      id: 'theme-gradient-sky-blue',
      value: 'gradient-sky-blue',
      label: 'Sky Blue',
      sidebarGradient: 'linear-gradient(165deg, rgba(76, 159, 213, 0.98), rgba(118, 205, 234, 0.99))',
      sidebarText: '#ffffff',
      mainBg: '#ffffff',
      mainGradient: 'linear-gradient(to bottom, rgba(118, 205, 234, 0.08), rgba(160, 216, 239, 0.12))',
      mainText: '#1d1c1d',
      selectedLabelBg: 'rgb(76, 159, 213)',
      selectedLabelText: '#ffffff'
    },
    {
      id: 'theme-gradient-deep-ocean',
      value: 'gradient-deep-ocean',
      label: 'Deep Ocean',
      sidebarGradient: 'linear-gradient(165deg, rgba(29, 43, 83, 0.98), rgba(48, 64, 128, 0.99))', // Original: rgba(62, 82, 163, 0.99)
      sidebarText: '#ffffff',
      mainBg: '#1a1f36', // Original: #10132b
      mainGradient: 'linear-gradient(to bottom, rgba(48, 64, 128, 0.15), rgba(29, 43, 83, 0.20))', // Original: rgba(62, 82, 163, 0.08), rgba(126, 156, 224, 0.12)
      mainText: '#e6e9f0', // Original: #e0e0e0
      selectedLabelBg: 'rgb(29, 43, 83)', // Darker start of the gradient
      selectedLabelText: '#ffffff'
    },
    {
      id: 'theme-gradient-coral-sunrise',
      value: 'gradient-coral-sunrise',
      label: 'Coral Sunrise',
      sidebarGradient: 'linear-gradient(165deg, rgba(245, 181, 207, 0.98), rgba(255, 170, 167, 0.99))',
      sidebarText: '#1d1c1d',
      mainBg: '#ffffff',
      mainGradient: 'linear-gradient(to bottom, rgba(255, 170, 167, 0.08), rgba(255, 211, 182, 0.12))',
      mainText: '#1d1c1d',
      selectedLabelBg: 'rgb(245, 181, 207)',
      selectedLabelText: '#1d1c1d'
    },
    {
      id: 'theme-gradient-meadow-green',
      value: 'gradient-meadow-green',
      label: 'Meadow Green',
      sidebarGradient: 'linear-gradient(165deg, rgba(93, 123, 66, 0.98), rgba(138, 154, 91, 0.99))',
      sidebarText: '#ffffff',
      mainBg: '#ffffff',
      mainGradient: 'linear-gradient(to bottom, rgba(138, 154, 91, 0.08), rgba(164, 188, 118, 0.12))',
      mainText: '#1d1c1d',
      selectedLabelBg: 'rgb(93, 123, 66)',
      selectedLabelText: '#ffffff'
    }
  ];

  return (
    <Tabs defaultValue="single-color-themes" className="w-full">
      <TabsList className="grid w-full grid-cols-3 h-12 mb-6 bg-muted/50">
        <TabsTrigger
          value="single-color-themes"
          className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground/90"
        >
          Single Colors
        </TabsTrigger>
        <TabsTrigger
          value="gradient-themes"
          className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground/90"
        >
          Gradients
        </TabsTrigger>
        <TabsTrigger
          value="text-size"
          className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground/90"
        >
          Text Size
        </TabsTrigger>
      </TabsList>

      <TabsContent value="single-color-themes" className="space-y-4 focus-visible:ring-0">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {singleColorThemes.map((theme) => (
            <ThemePreview
              key={theme.id}
              {...theme}
              selectedTheme={selectedTheme}
              onThemeChange={onThemeChange}
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="gradient-themes" className="space-y-4 focus-visible:ring-0">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {gradientThemes.map((theme) => (
            <ThemePreview
              key={theme.id}
              {...theme}
              selectedTheme={selectedTheme}
              onThemeChange={onThemeChange}
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="text-size" className="focus-visible:ring-0">
        <div className="pt-2 space-y-6">
          <div>
            <Label className="text-base font-medium block mb-4">Adjust Text Size</Label>
            <div className="grid grid-cols-3 gap-3 max-w-md">
              {(['small', 'medium', 'large'] as const).map((size) => {
                const isSelected = selectedTextSize === size;
                return (
                  <button
                    key={size}
                    onClick={() => onTextSizeChange?.(size)}
                    className={cn(
                      "py-3 px-4 border rounded-md text-center",
                      "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      "transition-colors duration-200",
                      isSelected
                        ? "bg-primary text-primary-foreground border-primary"
                        : "hover:bg-muted/80 border-border"
                    )}
                  >
                    <div className="space-y-1">
                      <div className={cn(
                        "font-medium",
                        size === 'small' && "text-sm",
                        size === 'medium' && "text-base",
                        size === 'large' && "text-lg"
                      )}>
                        {size.charAt(0).toUpperCase() + size.slice(1)}
                      </div>
                      <div className={cn(
                        "text-muted-foreground",
                        size === 'small' && "text-xs",
                        size === 'medium' && "text-sm",
                        size === 'large' && "text-base"
                      )}>
                        Aa
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          <div className="pt-4">
            <Label className="text-base font-medium block mb-3">Preview</Label>
            <div className={cn(
              "p-6 bg-muted/30 rounded-lg space-y-3",
              `text-size-${selectedTextSize}`
            )}>
              <p style={{ fontSize: 'var(--text-base)' }} className="font-medium text-foreground/90">
                Sample Text Preview
              </p>
              <p style={{ fontSize: 'var(--text-sm)' }} className="text-muted-foreground leading-relaxed">
                This preview shows how your text will appear throughout the application.
                The size you select will be applied to most text elements, helping you
                find the perfect balance between readability and screen space.
              </p>
              <div className="flex items-center gap-2 pt-2">
                <div
                  style={{
                    width: 'var(--icon-md)',
                    height: 'var(--icon-md)'
                  }}
                  className="bg-primary rounded-sm"
                />
                <span style={{ fontSize: 'var(--text-sm)' }} className="text-muted-foreground">
                  Icons and UI elements scale too
                </span>
              </div>
            </div>
          </div>

          <p className="text-xs text-muted-foreground mt-4">
            Text size changes will be applied immediately throughout the application.
          </p>
        </div>
      </TabsContent>
    </Tabs>
  );
};
